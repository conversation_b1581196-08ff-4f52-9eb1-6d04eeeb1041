'use client'

import { useState } from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './ChatHeader'
import { ChatMessages } from './ChatMessages'
import { ChatInput } from './ChatInput'
import { useChat } from '@/contexts/ChatContext'
import { typewriterEffect, sleep } from '@/lib/utils'

// Mock responses for demonstration
const mockResponses = [
  "I'm a helpful AI assistant created by OpenAI. I can help you with a wide variety of tasks including answering questions, writing, analysis, math, coding, and creative projects. What would you like to know or discuss?",
  
  "That's an interesting question! Let me think about that for a moment.\n\nHere are a few key points to consider:\n\n1. **First point**: This is an important aspect to understand\n2. **Second point**: Another crucial element\n3. **Third point**: A final consideration\n\nWould you like me to elaborate on any of these points?",
  
  "I'd be happy to help you with that! Here's a simple example:\n\n```javascript\nfunction greetUser(name) {\n  return `Hello, ${name}! Welcome to our application.`;\n}\n\nconsole.log(greetUser('World'));\n```\n\nThis function takes a name parameter and returns a personalized greeting. The template literal syntax (backticks) allows us to embed variables directly in the string.",
  
  "Great question! Here's what I think:\n\n> \"The best way to predict the future is to create it.\" - Peter Drucker\n\nThis quote really resonates with the idea that we have agency in shaping our outcomes. Rather than passively waiting for things to happen, we can take proactive steps to build the future we want to see.\n\nWhat aspects of this topic are you most curious about?",
  
  "I understand you're looking for information about this topic. Let me break it down:\n\n## Key Concepts\n\n- **Concept A**: This is fundamental to understanding the broader picture\n- **Concept B**: Builds upon the first concept\n- **Concept C**: Ties everything together\n\n### Practical Applications\n\n1. In everyday situations\n2. For professional development\n3. In academic contexts\n\nIs there a particular aspect you'd like me to focus on?"
]

export function ChatInterface() {
  const { 
    currentConversation, 
    addMessage, 
    updateMessage, 
    setIsTyping, 
    createNewConversation 
  } = useChat()

  const handleSendMessage = async (content) => {
    let conversationId = currentConversation?.id

    // Create new conversation if none exists
    if (!conversationId) {
      conversationId = createNewConversation()
    }

    // Add user message
    const userMessageId = addMessage(conversationId, {
      role: 'user',
      content
    })

    // Simulate AI response
    await simulateAIResponse(conversationId)
  }

  const simulateAIResponse = async (conversationId) => {
    // Set typing indicator
    setIsTyping(true)

    // Simulate thinking time
    await sleep(1000 + Math.random() * 2000)

    // Get random response
    const response = mockResponses[Math.floor(Math.random() * mockResponses.length)]

    // Add assistant message
    const assistantMessageId = addMessage(conversationId, {
      role: 'assistant',
      content: ''
    })

    // Clear typing indicator
    setIsTyping(false)

    // Simulate typewriter effect
    await typewriterEffect(
      response,
      (partialContent) => {
        updateMessage(conversationId, assistantMessageId, {
          content: partialContent
        })
      },
      20 // typing speed
    )
  }

  return (
    <div className="flex flex-col h-full bg-background">
      <ChatHeader />
      <ChatMessages />
      <ChatInput onSendMessage={handleSendMessage} />
    </div>
  )
}
