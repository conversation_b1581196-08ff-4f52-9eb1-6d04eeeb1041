{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/gpt-interface-next/client/src/components/ui/Button.js"], "sourcesContent": ["'use client'\n\nimport { forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\nimport { cva } from 'class-variance-authority'\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n  {\n    variants: {\n      variant: {\n        default: 'bg-primary text-white hover:bg-primary/90',\n        secondary: 'bg-gray-100 text-gray-900 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-100 dark:hover:bg-gray-700',\n        ghost: 'hover:bg-gray-100 dark:hover:bg-gray-800',\n        outline: 'border border-gray-300 bg-transparent hover:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-800',\n        destructive: 'bg-red-500 text-white hover:bg-red-600'\n      },\n      size: {\n        default: 'h-10 px-4 py-2',\n        sm: 'h-8 px-3 text-xs',\n        lg: 'h-12 px-8',\n        icon: 'h-10 w-10'\n      }\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default'\n    }\n  }\n)\n\nconst Button = forwardRef(({ className, variant, size, ...props }, ref) => {\n  return (\n    <button\n      className={cn(buttonVariants({ variant, size, className }))}\n      ref={ref}\n      {...props}\n    />\n  )\n})\n\nButton.displayName = 'Button'\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,iBAAiB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACvB,2OACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,OAAO;YACP,SAAS;YACT,aAAa;QACf;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,MAAM,uBAAS,CAAA,GAAA,4RAAA,CAAA,aAAU,AAAD,OAAE,QAAyC;QAAxC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAO;IAC/D,qBACE,4TAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEA,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/gpt-interface-next/client/src/components/ui/IconButton.js"], "sourcesContent": ["'use client'\n\nimport { forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nconst IconButton = forwardRef(({ \n  className, \n  children, \n  size = 'default',\n  variant = 'ghost',\n  ...props \n}, ref) => {\n  const sizeClasses = {\n    sm: 'h-8 w-8',\n    default: 'h-10 w-10',\n    lg: 'h-12 w-12'\n  }\n\n  const variantClasses = {\n    ghost: 'hover:bg-hover text-text-secondary hover:text-foreground',\n    outline: 'border border-border hover:bg-hover',\n    solid: 'bg-primary text-white hover:bg-primary/90'\n  }\n\n  return (\n    <button\n      ref={ref}\n      className={cn(\n        'inline-flex items-center justify-center rounded-md transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n        sizeClasses[size],\n        variantClasses[variant],\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n})\n\nIconButton.displayName = 'IconButton'\n\nexport { IconButton }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAU,AAAD,OAAE,QAM3B;QAN4B,EAC7B,SAAS,EACT,QAAQ,EACR,OAAO,SAAS,EAChB,UAAU,OAAO,EACjB,GAAG,OACJ;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,SAAS;QACT,IAAI;IACN;IAEA,MAAM,iBAAiB;QACrB,OAAO;QACP,SAAS;QACT,OAAO;IACT;IAEA,qBACE,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gOACA,WAAW,CAAC,KAAK,EACjB,cAAc,CAAC,QAAQ,EACvB;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;;AAEA,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/gpt-interface-next/client/src/components/ui/Input.js"], "sourcesContent": ["'use client'\n\nimport { forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nconst Input = forwardRef(({ className, type, ...props }, ref) => {\n  return (\n    <input\n      type={type}\n      className={cn(\n        'flex h-10 w-full rounded-md border border-input-border bg-input-bg px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-text-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n        className\n      )}\n      ref={ref}\n      {...props}\n    />\n  )\n})\n\nInput.displayName = 'Input'\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,4RAAA,CAAA,aAAU,AAAD,OAAE,QAAgC;QAA/B,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO;IACrD,qBACE,4TAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEA,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/gpt-interface-next/client/src/components/sidebar/ConversationItem.js"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { MessageSquare, Trash2, MoreH<PERSON>zontal } from 'lucide-react'\nimport { IconButton } from '@/components/ui/IconButton'\nimport { useChat } from '@/contexts/ChatContext'\nimport { formatTimestamp, cn } from '@/lib/utils'\n\nexport function ConversationItem({ conversation, isActive }) {\n  const [showActions, setShowActions] = useState(false)\n  const { setCurrentConversation, deleteConversation } = useChat()\n\n  const handleClick = () => {\n    setCurrentConversation(conversation.id)\n  }\n\n  const handleDelete = (e) => {\n    e.stopPropagation()\n    if (confirm('Are you sure you want to delete this conversation?')) {\n      deleteConversation(conversation.id)\n    }\n  }\n\n  const handleKeyDown = (e) => {\n    if (e.key === 'Enter' || e.key === ' ') {\n      e.preventDefault()\n      handleClick()\n    } else if (e.key === 'Delete' || e.key === 'Backspace') {\n      e.preventDefault()\n      handleDelete(e)\n    }\n  }\n\n  const handleMouseEnter = () => {\n    setShowActions(true)\n  }\n\n  const handleMouseLeave = () => {\n    setShowActions(false)\n  }\n\n  return (\n    <div\n      className={cn(\n        'group relative flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2',\n        isActive\n          ? 'bg-hover text-foreground'\n          : 'text-text-secondary hover:bg-hover hover:text-foreground'\n      )}\n      onClick={handleClick}\n      onKeyDown={handleKeyDown}\n      onMouseEnter={handleMouseEnter}\n      onMouseLeave={handleMouseLeave}\n      tabIndex={0}\n      role=\"button\"\n      aria-label={`Select conversation: ${conversation.title}`}\n      aria-current={isActive ? 'page' : undefined}\n    >\n      {/* Icon */}\n      <MessageSquare size={16} className=\"flex-shrink-0\" />\n\n      {/* Content */}\n      <div className=\"flex-1 min-w-0\">\n        <div className=\"text-sm font-medium truncate\">\n          {conversation.title}\n        </div>\n        <div className=\"text-xs text-text-muted\">\n          {formatTimestamp(conversation.updatedAt)}\n        </div>\n      </div>\n\n      {/* Actions */}\n      {(showActions || isActive) && (\n        <div className=\"flex items-center gap-1 flex-shrink-0\">\n          <IconButton\n            onClick={handleDelete}\n            size=\"sm\"\n            variant=\"ghost\"\n            className=\"opacity-0 group-hover:opacity-100 transition-opacity text-text-muted hover:text-red-500\"\n            title=\"Delete conversation\"\n          >\n            <Trash2 size={14} />\n          </IconButton>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAQO,SAAS,iBAAiB,KAA0B;QAA1B,EAAE,YAAY,EAAE,QAAQ,EAAE,GAA1B;;IAC/B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,sBAAsB,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAE7D,MAAM,cAAc;QAClB,uBAAuB,aAAa,EAAE;IACxC;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,eAAe;QACjB,IAAI,QAAQ,uDAAuD;YACjE,mBAAmB,aAAa,EAAE;QACpC;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,GAAG,KAAK,KAAK;YACtC,EAAE,cAAc;YAChB;QACF,OAAO,IAAI,EAAE,GAAG,KAAK,YAAY,EAAE,GAAG,KAAK,aAAa;YACtD,EAAE,cAAc;YAChB,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB;QACvB,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,eAAe;IACjB;IAEA,qBACE,4TAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iKACA,WACI,6BACA;QAEN,SAAS;QACT,WAAW;QACX,cAAc;QACd,cAAc;QACd,UAAU;QACV,MAAK;QACL,cAAY,AAAC,wBAA0C,OAAnB,aAAa,KAAK;QACtD,gBAAc,WAAW,SAAS;;0BAGlC,4TAAC,+SAAA,CAAA,gBAAa;gBAAC,MAAM;gBAAI,WAAU;;;;;;0BAGnC,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAI,WAAU;kCACZ,aAAa,KAAK;;;;;;kCAErB,4TAAC;wBAAI,WAAU;kCACZ,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,aAAa,SAAS;;;;;;;;;;;;YAK1C,CAAC,eAAe,QAAQ,mBACvB,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC,wIAAA,CAAA,aAAU;oBACT,SAAS;oBACT,MAAK;oBACL,SAAQ;oBACR,WAAU;oBACV,OAAM;8BAEN,cAAA,4TAAC,iSAAA,CAAA,SAAM;wBAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;AAM1B;GA/EgB;;QAEyC,iIAAA,CAAA,UAAO;;;KAFhD", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/gpt-interface-next/client/src/components/sidebar/Sidebar.js"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Plus, Search, Menu, X, Sun, Moon, <PERSON> } from 'lucide-react'\nimport { But<PERSON> } from '@/components/ui/Button'\nimport { IconButton } from '@/components/ui/IconButton'\nimport { Input } from '@/components/ui/Input'\nimport { ConversationItem } from './ConversationItem'\nimport { useChat } from '@/contexts/ChatContext'\nimport { useTheme } from '@/contexts/ThemeContext'\nimport { cn } from '@/lib/utils'\n\nexport function Sidebar() {\n  const [searchQuery, setSearchQuery] = useState('')\n  const {\n    conversations,\n    currentConversationId,\n    createNewConversation,\n    sidebarOpen,\n    toggleSidebar,\n    setSidebarOpen\n  } = useChat()\n  const { theme, setTheme, isDark } = useTheme()\n\n  const filteredConversations = conversations.filter(conv =>\n    conv.title.toLowerCase().includes(searchQuery.toLowerCase())\n  )\n\n  const handleNewChat = () => {\n    createNewConversation()\n  }\n\n  const themeOptions = [\n    { value: 'light', icon: Sun, label: 'Light' },\n    { value: 'dark', icon: Moon, label: 'Dark' },\n    { value: 'system', icon: Monitor, label: 'System' }\n  ]\n\n  return (\n    <>\n      {/* Mobile Overlay */}\n      {sidebarOpen && (\n        <div\n          className=\"fixed inset-0 bg-black/50 z-40 lg:hidden\"\n          onClick={() => setSidebarOpen(false)}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div className={cn(\n        'fixed left-0 top-0 h-full w-80 bg-sidebar-bg border-r border-border z-50 transform transition-transform duration-300 ease-in-out lg:relative lg:translate-x-0 lg:w-80 md:w-72 sm:w-64',\n        sidebarOpen ? 'translate-x-0' : '-translate-x-full'\n      )}>\n        <div className=\"flex flex-col h-full\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between p-4 border-b border-border\">\n            <h1 className=\"text-lg font-semibold text-foreground\">ChatGPT</h1>\n            <div className=\"flex items-center gap-2\">\n              {/* Theme Toggle */}\n              <div className=\"flex items-center bg-hover rounded-lg p-1\">\n                {themeOptions.map(({ value, icon: Icon, label }) => (\n                  <IconButton\n                    key={value}\n                    onClick={() => setTheme(value)}\n                    size=\"sm\"\n                    variant={theme === value ? 'solid' : 'ghost'}\n                    className={cn(\n                      'h-8 w-8',\n                      theme === value && 'bg-primary text-white'\n                    )}\n                    title={label}\n                  >\n                    <Icon size={14} />\n                  </IconButton>\n                ))}\n              </div>\n\n              {/* Close button (mobile only) */}\n              <IconButton\n                onClick={toggleSidebar}\n                size=\"sm\"\n                variant=\"ghost\"\n                className=\"lg:hidden\"\n              >\n                <X size={16} />\n              </IconButton>\n            </div>\n          </div>\n\n          {/* New Chat Button */}\n          <div className=\"p-4\">\n            <Button\n              onClick={handleNewChat}\n              className=\"w-full justify-start gap-3\"\n              variant=\"outline\"\n            >\n              <Plus size={16} />\n              New Chat\n            </Button>\n          </div>\n\n          {/* Search */}\n          <div className=\"px-4 pb-4\">\n            <div className=\"relative\">\n              <Search size={16} className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-text-muted\" />\n              <Input\n                placeholder=\"Search conversations...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"pl-10\"\n              />\n            </div>\n          </div>\n\n          {/* Conversations List */}\n          <div\n            className=\"flex-1 overflow-y-auto px-4 pb-4\"\n            role=\"navigation\"\n            aria-label=\"Conversation history\"\n          >\n            {filteredConversations.length === 0 ? (\n              <div className=\"text-center text-text-muted py-8\">\n                {searchQuery ? 'No conversations found' : 'No conversations yet'}\n              </div>\n            ) : (\n              <div className=\"space-y-1\">\n                {filteredConversations.map((conversation) => (\n                  <ConversationItem\n                    key={conversation.id}\n                    conversation={conversation}\n                    isActive={conversation.id === currentConversationId}\n                  />\n                ))}\n              </div>\n            )}\n          </div>\n\n          {/* Footer */}\n          <div className=\"p-4 border-t border-border\">\n            <div className=\"text-xs text-text-muted text-center\">\n              ChatGPT Clone v1.0\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAYO,SAAS;;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EACJ,aAAa,EACb,qBAAqB,EACrB,qBAAqB,EACrB,WAAW,EACX,aAAa,EACb,cAAc,EACf,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IACV,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD;IAE3C,MAAM,wBAAwB,cAAc,MAAM,CAAC,CAAA,OACjD,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;IAG3D,MAAM,gBAAgB;QACpB;IACF;IAEA,MAAM,eAAe;QACnB;YAAE,OAAO;YAAS,MAAM,uRAAA,CAAA,MAAG;YAAE,OAAO;QAAQ;QAC5C;YAAE,OAAO;YAAQ,MAAM,yRAAA,CAAA,OAAI;YAAE,OAAO;QAAO;QAC3C;YAAE,OAAO;YAAU,MAAM,+RAAA,CAAA,UAAO;YAAE,OAAO;QAAS;KACnD;IAED,qBACE;;YAEG,6BACC,4TAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAKlC,4TAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,yLACA,cAAc,kBAAkB;0BAEhC,cAAA,4TAAC;oBAAI,WAAU;;sCAEb,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,4TAAC;oCAAI,WAAU;;sDAEb,4TAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC;oDAAC,EAAE,KAAK,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE;qEAC7C,4TAAC,wIAAA,CAAA,aAAU;oDAET,SAAS,IAAM,SAAS;oDACxB,MAAK;oDACL,SAAS,UAAU,QAAQ,UAAU;oDACrC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,WACA,UAAU,SAAS;oDAErB,OAAO;8DAEP,cAAA,4TAAC;wDAAK,MAAM;;;;;;mDAVP;;;;;;;;;;;sDAgBX,4TAAC,wIAAA,CAAA,aAAU;4CACT,SAAS;4CACT,MAAK;4CACL,SAAQ;4CACR,WAAU;sDAEV,cAAA,4TAAC,mRAAA,CAAA,IAAC;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;sCAMf,4TAAC;4BAAI,WAAU;sCACb,cAAA,4TAAC,oIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;gCACV,SAAQ;;kDAER,4TAAC,yRAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;oCAAM;;;;;;;;;;;;sCAMtB,4TAAC;4BAAI,WAAU;sCACb,cAAA,4TAAC;gCAAI,WAAU;;kDACb,4TAAC,6RAAA,CAAA,SAAM;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC5B,4TAAC,mIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;;;;;;sCAMhB,4TAAC;4BACC,WAAU;4BACV,MAAK;4BACL,cAAW;sCAEV,sBAAsB,MAAM,KAAK,kBAChC,4TAAC;gCAAI,WAAU;0CACZ,cAAc,2BAA2B;;;;;qDAG5C,4TAAC;gCAAI,WAAU;0CACZ,sBAAsB,GAAG,CAAC,CAAC,6BAC1B,4TAAC,mJAAA,CAAA,mBAAgB;wCAEf,cAAc;wCACd,UAAU,aAAa,EAAE,KAAK;uCAFzB,aAAa,EAAE;;;;;;;;;;;;;;;sCAU9B,4TAAC;4BAAI,WAAU;sCACb,cAAA,4TAAC;gCAAI,WAAU;0CAAsC;;;;;;;;;;;;;;;;;;;;;;;;AAQjE;GAvIgB;;QASV,iIAAA,CAAA,UAAO;QACyB,kIAAA,CAAA,WAAQ;;;KAV9B", "debugId": null}}, {"offset": {"line": 578, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/gpt-interface-next/client/src/components/chat/ChatHeader.js"], "sourcesContent": ["'use client'\n\nimport { Menu, MoreVertical } from 'lucide-react'\nimport { IconButton } from '@/components/ui/IconButton'\nimport { useChat } from '@/contexts/ChatContext'\n\nexport function ChatHeader() {\n  const { currentConversation, toggleSidebar, sidebarOpen } = useChat()\n\n  return (\n    <div className=\"flex items-center justify-between p-4 border-b border-border bg-background\">\n      <div className=\"flex items-center gap-3\">\n        {/* Sidebar toggle (mobile) */}\n        <IconButton\n          onClick={toggleSidebar}\n          variant=\"ghost\"\n          className=\"lg:hidden\"\n          title={sidebarOpen ? \"Close sidebar\" : \"Open sidebar\"}\n        >\n          <Menu size={20} />\n        </IconButton>\n\n        {/* Conversation title */}\n        <div>\n          <h1 className=\"text-lg font-semibold text-foreground\">\n            {currentConversation?.title || 'ChatGPT'}\n          </h1>\n          {currentConversation && (\n            <p className=\"text-sm text-text-secondary\">\n              {currentConversation.messages.length} messages\n            </p>\n          )}\n        </div>\n      </div>\n\n      {/* Actions */}\n      <div className=\"flex items-center gap-2\">\n        <IconButton\n          variant=\"ghost\"\n          title=\"More options\"\n        >\n          <MoreVertical size={20} />\n        </IconButton>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;;;AAJA;;;;AAMO,SAAS;;IACd,MAAM,EAAE,mBAAmB,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAElE,qBACE,4TAAC;QAAI,WAAU;;0BACb,4TAAC;gBAAI,WAAU;;kCAEb,4TAAC,wIAAA,CAAA,aAAU;wBACT,SAAS;wBACT,SAAQ;wBACR,WAAU;wBACV,OAAO,cAAc,kBAAkB;kCAEvC,cAAA,4TAAC,yRAAA,CAAA,OAAI;4BAAC,MAAM;;;;;;;;;;;kCAId,4TAAC;;0CACC,4TAAC;gCAAG,WAAU;0CACX,CAAA,gCAAA,0CAAA,oBAAqB,KAAK,KAAI;;;;;;4BAEhC,qCACC,4TAAC;gCAAE,WAAU;;oCACV,oBAAoB,QAAQ,CAAC,MAAM;oCAAC;;;;;;;;;;;;;;;;;;;0BAO7C,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC,wIAAA,CAAA,aAAU;oBACT,SAAQ;oBACR,OAAM;8BAEN,cAAA,4TAAC,iTAAA,CAAA,eAAY;wBAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;AAK9B;GAxCgB;;QAC8C,iIAAA,CAAA,UAAO;;;KADrD", "debugId": null}}, {"offset": {"line": 697, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/gpt-interface-next/client/src/components/ui/Avatar.js"], "sourcesContent": ["'use client'\n\nimport { forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nconst Avatar = forwardRef(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full',\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = 'Avatar'\n\nconst AvatarImage = forwardRef(({ className, ...props }, ref) => (\n  <img\n    ref={ref}\n    className={cn('aspect-square h-full w-full object-cover', className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = 'AvatarImage'\n\nconst AvatarFallback = forwardRef(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'flex h-full w-full items-center justify-center rounded-full bg-gray-100 text-gray-600 text-sm font-medium dark:bg-gray-800 dark:text-gray-300',\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = 'AvatarFallback'\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,uBAAS,CAAA,GAAA,4RAAA,CAAA,aAAU,AAAD,OAAE,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBAChD,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;;;AAGb,OAAO,WAAW,GAAG;AAErB,MAAM,4BAAc,CAAA,GAAA,4RAAA,CAAA,aAAU,AAAD,QAAE,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACrD,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QACzD,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,+BAAiB,CAAA,GAAA,4RAAA,CAAA,aAAU,AAAD,QAAE,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxD,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iJACA;QAED,GAAG,KAAK;;;;;;;;AAGb,eAAe,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 768, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/gpt-interface-next/client/src/components/chat/MessageBubble.js"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { <PERSON><PERSON>, Check, User, Bo<PERSON> } from 'lucide-react'\nimport { Avatar, AvatarFallback } from '@/components/ui/Avatar'\nimport { IconButton } from '@/components/ui/IconButton'\nimport { copyToClipboard, formatTimestamp } from '@/lib/utils'\nimport ReactMarkdown from 'react-markdown'\nimport { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'\nimport { oneDark, oneLight } from 'react-syntax-highlighter/dist/esm/styles/prism'\nimport { useTheme } from '@/contexts/ThemeContext'\n\nexport function MessageBubble({ message, isLast = false }) {\n  const [copied, setCopied] = useState(false)\n  const { isDark } = useTheme()\n  const isUser = message.role === 'user'\n  const isAssistant = message.role === 'assistant'\n\n  const handleCopy = async () => {\n    const success = await copyToClipboard(message.content)\n    if (success) {\n      setCopied(true)\n      setTimeout(() => setCopied(false), 2000)\n    }\n  }\n\n  const MarkdownComponents = {\n    code({ node, inline, className, children, ...props }) {\n      const match = /language-(\\w+)/.exec(className || '')\n      const language = match ? match[1] : ''\n\n      if (!inline && language) {\n        return (\n          <div className=\"relative group\">\n            <div className=\"flex items-center justify-between bg-gray-800 text-gray-200 px-4 py-2 text-xs rounded-t-md\">\n              <span>{language}</span>\n              <button\n                onClick={() => copyToClipboard(String(children).replace(/\\n$/, ''))}\n                className=\"opacity-0 group-hover:opacity-100 transition-opacity hover:bg-gray-700 p-1 rounded\"\n              >\n                <Copy size={14} />\n              </button>\n            </div>\n            <SyntaxHighlighter\n              style={isDark ? oneDark : oneLight}\n              language={language}\n              PreTag=\"div\"\n              className=\"!mt-0 !rounded-t-none\"\n              {...props}\n            >\n              {String(children).replace(/\\n$/, '')}\n            </SyntaxHighlighter>\n          </div>\n        )\n      }\n\n      return (\n        <code\n          className=\"bg-gray-100 dark:bg-gray-800 px-1.5 py-0.5 rounded text-sm font-mono\"\n          {...props}\n        >\n          {children}\n        </code>\n      )\n    },\n\n    pre({ children }) {\n      return <div className=\"overflow-x-auto\">{children}</div>\n    },\n\n    p({ children }) {\n      return <p className=\"mb-2 last:mb-0\">{children}</p>\n    },\n\n    ul({ children }) {\n      return <ul className=\"list-disc list-inside mb-2 space-y-1\">{children}</ul>\n    },\n\n    ol({ children }) {\n      return <ol className=\"list-decimal list-inside mb-2 space-y-1\">{children}</ol>\n    },\n\n    li({ children }) {\n      return <li className=\"ml-2\">{children}</li>\n    },\n\n    blockquote({ children }) {\n      return (\n        <blockquote className=\"border-l-4 border-gray-300 dark:border-gray-600 pl-4 italic my-2\">\n          {children}\n        </blockquote>\n      )\n    },\n\n    h1({ children }) {\n      return <h1 className=\"text-xl font-bold mb-2\">{children}</h1>\n    },\n\n    h2({ children }) {\n      return <h2 className=\"text-lg font-bold mb-2\">{children}</h2>\n    },\n\n    h3({ children }) {\n      return <h3 className=\"text-base font-bold mb-2\">{children}</h3>\n    },\n\n    a({ href, children }) {\n      return (\n        <a\n          href={href}\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n          className=\"text-primary hover:underline\"\n        >\n          {children}\n        </a>\n      )\n    }\n  }\n\n  return (\n    <div className={`group relative ${isUser ? 'ml-auto max-w-[90%] sm:max-w-[80%]' : 'mr-auto'}`}>\n      <div className=\"flex items-start gap-2 sm:gap-3 p-3 sm:p-4\">\n        {/* Avatar */}\n        <Avatar className=\"flex-shrink-0\">\n          <AvatarFallback className={isUser ? 'bg-primary text-white' : 'bg-gray-200 dark:bg-gray-700'}>\n            {isUser ? <User size={16} /> : <Bot size={16} />}\n          </AvatarFallback>\n        </Avatar>\n\n        {/* Message Content */}\n        <div className=\"flex-1 min-w-0\">\n          <div className=\"flex items-center gap-2 mb-1\">\n            <span className=\"text-sm font-medium text-foreground\">\n              {isUser ? 'You' : 'ChatGPT'}\n            </span>\n            <span className=\"text-xs text-text-muted\">\n              {formatTimestamp(message.timestamp)}\n            </span>\n          </div>\n\n          <div className={`\n            rounded-lg px-4 py-3 \n            ${isUser\n              ? 'bg-user-message text-foreground'\n              : 'bg-assistant-message text-foreground'\n            }\n          `}>\n            {isAssistant ? (\n              <div className=\"prose prose-sm max-w-none dark:prose-invert\">\n                <ReactMarkdown components={MarkdownComponents}>\n                  {message.content}\n                </ReactMarkdown>\n              </div>\n            ) : (\n              <div className=\"whitespace-pre-wrap break-words\">\n                {message.content}\n              </div>\n            )}\n          </div>\n\n          {/* Copy Button */}\n          <div className=\"flex justify-end mt-2\">\n            <IconButton\n              onClick={handleCopy}\n              size=\"sm\"\n              className=\"opacity-0 group-hover:opacity-100 transition-opacity\"\n              title=\"Copy message\"\n            >\n              {copied ? <Check size={14} /> : <Copy size={14} />}\n            </IconButton>\n          </div>\n        </div>\n      </div>\n\n      {/* Typing Indicator */}\n      {message.isTyping && (\n        <div className=\"flex items-center gap-3 p-4 pt-0\">\n          <div className=\"w-10\" /> {/* Avatar spacer */}\n          <div className=\"typing-indicator\">\n            <div className=\"typing-dot\"></div>\n            <div className=\"typing-dot\"></div>\n            <div className=\"typing-dot\"></div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;;;AAVA;;;;;;;;;;AAYO,SAAS,cAAc,KAA2B;QAA3B,EAAE,OAAO,EAAE,SAAS,KAAK,EAAE,GAA3B;;IAC5B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD;IAC1B,MAAM,SAAS,QAAQ,IAAI,KAAK;IAChC,MAAM,cAAc,QAAQ,IAAI,KAAK;IAErC,MAAM,aAAa;QACjB,MAAM,UAAU,MAAM,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,OAAO;QACrD,IAAI,SAAS;YACX,UAAU;YACV,WAAW,IAAM,UAAU,QAAQ;QACrC;IACF;IAEA,MAAM,qBAAqB;QACzB,MAAK,KAA+C;gBAA/C,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,GAA/C;YACH,MAAM,QAAQ,iBAAiB,IAAI,CAAC,aAAa;YACjD,MAAM,WAAW,QAAQ,KAAK,CAAC,EAAE,GAAG;YAEpC,IAAI,CAAC,UAAU,UAAU;gBACvB,qBACE,4TAAC;oBAAI,WAAU;;sCACb,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;8CAAM;;;;;;8CACP,4TAAC;oCACC,SAAS,IAAM,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,UAAU,OAAO,CAAC,OAAO;oCAC/D,WAAU;8CAEV,cAAA,4TAAC,yRAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;;;;;;;sCAGhB,4TAAC,+SAAA,CAAA,QAAiB;4BAChB,OAAO,SAAS,0UAAA,CAAA,UAAO,GAAG,4UAAA,CAAA,WAAQ;4BAClC,UAAU;4BACV,QAAO;4BACP,WAAU;4BACT,GAAG,KAAK;sCAER,OAAO,UAAU,OAAO,CAAC,OAAO;;;;;;;;;;;;YAIzC;YAEA,qBACE,4TAAC;gBACC,WAAU;gBACT,GAAG,KAAK;0BAER;;;;;;QAGP;QAEA,KAAI,KAAY;gBAAZ,EAAE,QAAQ,EAAE,GAAZ;YACF,qBAAO,4TAAC;gBAAI,WAAU;0BAAmB;;;;;;QAC3C;QAEA,GAAE,KAAY;gBAAZ,EAAE,QAAQ,EAAE,GAAZ;YACA,qBAAO,4TAAC;gBAAE,WAAU;0BAAkB;;;;;;QACxC;QAEA,IAAG,KAAY;gBAAZ,EAAE,QAAQ,EAAE,GAAZ;YACD,qBAAO,4TAAC;gBAAG,WAAU;0BAAwC;;;;;;QAC/D;QAEA,IAAG,KAAY;gBAAZ,EAAE,QAAQ,EAAE,GAAZ;YACD,qBAAO,4TAAC;gBAAG,WAAU;0BAA2C;;;;;;QAClE;QAEA,IAAG,KAAY;gBAAZ,EAAE,QAAQ,EAAE,GAAZ;YACD,qBAAO,4TAAC;gBAAG,WAAU;0BAAQ;;;;;;QAC/B;QAEA,YAAW,KAAY;gBAAZ,EAAE,QAAQ,EAAE,GAAZ;YACT,qBACE,4TAAC;gBAAW,WAAU;0BACnB;;;;;;QAGP;QAEA,IAAG,KAAY;gBAAZ,EAAE,QAAQ,EAAE,GAAZ;YACD,qBAAO,4TAAC;gBAAG,WAAU;0BAA0B;;;;;;QACjD;QAEA,IAAG,KAAY;gBAAZ,EAAE,QAAQ,EAAE,GAAZ;YACD,qBAAO,4TAAC;gBAAG,WAAU;0BAA0B;;;;;;QACjD;QAEA,IAAG,KAAY;gBAAZ,EAAE,QAAQ,EAAE,GAAZ;YACD,qBAAO,4TAAC;gBAAG,WAAU;0BAA4B;;;;;;QACnD;QAEA,GAAE,KAAkB;gBAAlB,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAlB;YACA,qBACE,4TAAC;gBACC,MAAM;gBACN,QAAO;gBACP,KAAI;gBACJ,WAAU;0BAET;;;;;;QAGP;IACF;IAEA,qBACE,4TAAC;QAAI,WAAW,AAAC,kBAA2E,OAA1D,SAAS,uCAAuC;;0BAChF,4TAAC;gBAAI,WAAU;;kCAEb,4TAAC,oIAAA,CAAA,SAAM;wBAAC,WAAU;kCAChB,cAAA,4TAAC,oIAAA,CAAA,iBAAc;4BAAC,WAAW,SAAS,0BAA0B;sCAC3D,uBAAS,4TAAC,yRAAA,CAAA,OAAI;gCAAC,MAAM;;;;;qDAAS,4TAAC,uRAAA,CAAA,MAAG;gCAAC,MAAM;;;;;;;;;;;;;;;;kCAK9C,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;;kDACb,4TAAC;wCAAK,WAAU;kDACb,SAAS,QAAQ;;;;;;kDAEpB,4TAAC;wCAAK,WAAU;kDACb,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,SAAS;;;;;;;;;;;;0CAItC,4TAAC;gCAAI,WAAW,AAAC,oDAKd,OAHC,SACE,oCACA,wCACH;0CAEA,4BACC,4TAAC;oCAAI,WAAU;8CACb,cAAA,4TAAC,mTAAA,CAAA,UAAa;wCAAC,YAAY;kDACxB,QAAQ,OAAO;;;;;;;;;;yDAIpB,4TAAC;oCAAI,WAAU;8CACZ,QAAQ,OAAO;;;;;;;;;;;0CAMtB,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC,wIAAA,CAAA,aAAU;oCACT,SAAS;oCACT,MAAK;oCACL,WAAU;oCACV,OAAM;8CAEL,uBAAS,4TAAC,2RAAA,CAAA,QAAK;wCAAC,MAAM;;;;;6DAAS,4TAAC,yRAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOnD,QAAQ,QAAQ,kBACf,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAI,WAAU;;;;;;oBAAS;kCACxB,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;GAhLgB;;QAEK,kIAAA,CAAA,WAAQ;;;KAFb", "debugId": null}}, {"offset": {"line": 1197, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/gpt-interface-next/client/src/components/chat/WelcomeScreen.js"], "sourcesContent": ["'use client'\n\nimport { MessageSquare, Lightbulb, Code, BookOpen } from 'lucide-react'\nimport { Button } from '@/components/ui/Button'\nimport { useChat } from '@/contexts/ChatContext'\n\nexport function WelcomeScreen() {\n  const { createNewConversation } = useChat()\n\n  const examples = [\n    {\n      icon: MessageSquare,\n      title: \"Explain quantum computing\",\n      subtitle: \"in simple terms\"\n    },\n    {\n      icon: Lightbulb,\n      title: \"Got any creative ideas\",\n      subtitle: \"for a 10 year old's birthday?\"\n    },\n    {\n      icon: Code,\n      title: \"How do I make an HTTP request\",\n      subtitle: \"in Javascript?\"\n    },\n    {\n      icon: BookOpen,\n      title: \"Help me write a short story\",\n      subtitle: \"about a magical forest\"\n    }\n  ]\n\n  const handleExampleClick = (example) => {\n    const conversationId = createNewConversation()\n    // You could auto-send the example message here if desired\n  }\n\n  return (\n    <div className=\"flex-1 flex items-center justify-center p-8\">\n      <div className=\"max-w-3xl w-full text-center\">\n        {/* Logo/Title */}\n        <div className=\"mb-8\">\n          <div className=\"w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <MessageSquare className=\"w-10 h-10 text-primary\" />\n          </div>\n          <h1 className=\"text-3xl font-bold text-foreground mb-2\">\n            Welcome to ChatGPT\n          </h1>\n          <p className=\"text-text-secondary text-lg\">\n            How can I help you today?\n          </p>\n        </div>\n\n        {/* Example prompts */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-8\">\n          {examples.map((example, index) => {\n            const Icon = example.icon\n            return (\n              <button\n                key={index}\n                onClick={() => handleExampleClick(example)}\n                className=\"p-4 border border-border rounded-lg hover:bg-hover transition-colors text-left group\"\n              >\n                <div className=\"flex items-start gap-3\">\n                  <Icon className=\"w-5 h-5 text-text-secondary group-hover:text-primary transition-colors flex-shrink-0 mt-0.5\" />\n                  <div>\n                    <div className=\"font-medium text-foreground group-hover:text-primary transition-colors\">\n                      {example.title}\n                    </div>\n                    <div className=\"text-sm text-text-secondary\">\n                      {example.subtitle}\n                    </div>\n                  </div>\n                </div>\n              </button>\n            )\n          })}\n        </div>\n\n        {/* Capabilities */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 text-sm\">\n          <div className=\"space-y-2\">\n            <div className=\"font-medium text-foreground\">💡 Examples</div>\n            <div className=\"text-text-secondary space-y-1\">\n              <div>\"Explain quantum computing in simple terms\"</div>\n              <div>\"Got any creative ideas for a 10 year old's birthday?\"</div>\n              <div>\"How do I make an HTTP request in Javascript?\"</div>\n            </div>\n          </div>\n          \n          <div className=\"space-y-2\">\n            <div className=\"font-medium text-foreground\">⚡ Capabilities</div>\n            <div className=\"text-text-secondary space-y-1\">\n              <div>Remembers what user said earlier in the conversation</div>\n              <div>Allows user to provide follow-up corrections</div>\n              <div>Trained to decline inappropriate requests</div>\n            </div>\n          </div>\n          \n          <div className=\"space-y-2\">\n            <div className=\"font-medium text-foreground\">⚠️ Limitations</div>\n            <div className=\"text-text-secondary space-y-1\">\n              <div>May occasionally generate incorrect information</div>\n              <div>May occasionally produce harmful instructions or biased content</div>\n              <div>Limited knowledge of world and events after 2021</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AACA;AACA;;;AAJA;;;;AAMO,SAAS;;IACd,MAAM,EAAE,qBAAqB,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAExC,MAAM,WAAW;QACf;YACE,MAAM,+SAAA,CAAA,gBAAa;YACnB,OAAO;YACP,UAAU;QACZ;QACA;YACE,MAAM,mSAAA,CAAA,YAAS;YACf,OAAO;YACP,UAAU;QACZ;QACA;YACE,MAAM,yRAAA,CAAA,OAAI;YACV,OAAO;YACP,UAAU;QACZ;QACA;YACE,MAAM,qSAAA,CAAA,WAAQ;YACd,OAAO;YACP,UAAU;QACZ;KACD;IAED,MAAM,qBAAqB,CAAC;QAC1B,MAAM,iBAAiB;IACvB,0DAA0D;IAC5D;IAEA,qBACE,4TAAC;QAAI,WAAU;kBACb,cAAA,4TAAC;YAAI,WAAU;;8BAEb,4TAAC;oBAAI,WAAU;;sCACb,4TAAC;4BAAI,WAAU;sCACb,cAAA,4TAAC,+SAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;;;;;;sCAE3B,4TAAC;4BAAG,WAAU;sCAA0C;;;;;;sCAGxD,4TAAC;4BAAE,WAAU;sCAA8B;;;;;;;;;;;;8BAM7C,4TAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS;wBACtB,MAAM,OAAO,QAAQ,IAAI;wBACzB,qBACE,4TAAC;4BAEC,SAAS,IAAM,mBAAmB;4BAClC,WAAU;sCAEV,cAAA,4TAAC;gCAAI,WAAU;;kDACb,4TAAC;wCAAK,WAAU;;;;;;kDAChB,4TAAC;;0DACC,4TAAC;gDAAI,WAAU;0DACZ,QAAQ,KAAK;;;;;;0DAEhB,4TAAC;gDAAI,WAAU;0DACZ,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;2BAXlB;;;;;oBAiBX;;;;;;8BAIF,4TAAC;oBAAI,WAAU;;sCACb,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAI,WAAU;8CAA8B;;;;;;8CAC7C,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;sDAAI;;;;;;sDACL,4TAAC;sDAAI;;;;;;sDACL,4TAAC;sDAAI;;;;;;;;;;;;;;;;;;sCAIT,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAI,WAAU;8CAA8B;;;;;;8CAC7C,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;sDAAI;;;;;;sDACL,4TAAC;sDAAI;;;;;;sDACL,4TAAC;sDAAI;;;;;;;;;;;;;;;;;;sCAIT,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAI,WAAU;8CAA8B;;;;;;8CAC7C,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;sDAAI;;;;;;sDACL,4TAAC;sDAAI;;;;;;sDACL,4TAAC;sDAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnB;GAzGgB;;QACoB,iIAAA,CAAA,UAAO;;;KAD3B", "debugId": null}}, {"offset": {"line": 1523, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/gpt-interface-next/client/src/components/chat/ChatMessages.js"], "sourcesContent": ["'use client'\n\nimport { useEffect, useRef } from 'react'\nimport { MessageBubble } from './MessageBubble'\nimport { WelcomeScreen } from './WelcomeScreen'\nimport { useChat } from '@/contexts/ChatContext'\n\nexport function ChatMessages() {\n  const { currentConversation, isTyping } = useChat()\n  const messagesEndRef = useRef(null)\n  const messagesContainerRef = useRef(null)\n\n  // Auto-scroll to bottom when new messages arrive\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' })\n    }\n  }, [currentConversation?.messages, isTyping])\n\n  if (!currentConversation) {\n    return <WelcomeScreen />\n  }\n\n  const messages = currentConversation.messages || []\n\n  return (\n    <div\n      ref={messagesContainerRef}\n      className=\"flex-1 overflow-y-auto\"\n      role=\"log\"\n      aria-label=\"Chat messages\"\n      aria-live=\"polite\"\n    >\n      <div className=\"max-w-4xl mx-auto\">\n        {messages.length === 0 ? (\n          <div className=\"flex items-center justify-center h-full p-8\">\n            <div className=\"text-center max-w-md\">\n              <div className=\"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <svg\n                  className=\"w-8 h-8 text-primary\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                  />\n                </svg>\n              </div>\n              <h3 className=\"text-lg font-medium text-foreground mb-2\">\n                New Conversation\n              </h3>\n              <p className=\"text-text-secondary\">\n                Send a message to start chatting with ChatGPT.\n              </p>\n            </div>\n          </div>\n        ) : (\n          <div className=\"space-y-0\">\n            {messages.map((message, index) => (\n              <MessageBubble\n                key={message.id}\n                message={message}\n                isLast={index === messages.length - 1}\n              />\n            ))}\n\n            {/* Typing indicator */}\n            {isTyping && (\n              <MessageBubble\n                message={{\n                  id: 'typing',\n                  role: 'assistant',\n                  content: '',\n                  timestamp: new Date().toISOString(),\n                  isTyping: true\n                }}\n              />\n            )}\n          </div>\n        )}\n\n        {/* Scroll anchor */}\n        <div ref={messagesEndRef} />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOO,SAAS;;IACd,MAAM,EAAE,mBAAmB,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAChD,MAAM,iBAAiB,CAAA,GAAA,4RAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,MAAM,uBAAuB,CAAA,GAAA,4RAAA,CAAA,SAAM,AAAD,EAAE;IAEpC,iDAAiD;IACjD,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,eAAe,OAAO,EAAE;gBAC1B,eAAe,OAAO,CAAC,cAAc,CAAC;oBAAE,UAAU;gBAAS;YAC7D;QACF;iCAAG;QAAC,gCAAA,0CAAA,oBAAqB,QAAQ;QAAE;KAAS;IAE5C,IAAI,CAAC,qBAAqB;QACxB,qBAAO,4TAAC,6IAAA,CAAA,gBAAa;;;;;IACvB;IAEA,MAAM,WAAW,oBAAoB,QAAQ,IAAI,EAAE;IAEnD,qBACE,4TAAC;QACC,KAAK;QACL,WAAU;QACV,MAAK;QACL,cAAW;QACX,aAAU;kBAEV,cAAA,4TAAC;YAAI,WAAU;;gBACZ,SAAS,MAAM,KAAK,kBACnB,4TAAC;oBAAI,WAAU;8BACb,cAAA,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC;oCACC,WAAU;oCACV,MAAK;oCACL,QAAO;oCACP,SAAQ;8CAER,cAAA,4TAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,aAAa;wCACb,GAAE;;;;;;;;;;;;;;;;0CAIR,4TAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,4TAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;;;;;yCAMvC,4TAAC;oBAAI,WAAU;;wBACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,4TAAC,6IAAA,CAAA,gBAAa;gCAEZ,SAAS;gCACT,QAAQ,UAAU,SAAS,MAAM,GAAG;+BAF/B,QAAQ,EAAE;;;;;wBAOlB,0BACC,4TAAC,6IAAA,CAAA,gBAAa;4BACZ,SAAS;gCACP,IAAI;gCACJ,MAAM;gCACN,SAAS;gCACT,WAAW,IAAI,OAAO,WAAW;gCACjC,UAAU;4BACZ;;;;;;;;;;;;8BAOR,4TAAC;oBAAI,KAAK;;;;;;;;;;;;;;;;;AAIlB;GAnFgB;;QAC4B,iIAAA,CAAA,UAAO;;;KADnC", "debugId": null}}, {"offset": {"line": 1697, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/gpt-interface-next/client/src/components/chat/ChatInput.js"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { Send, Square } from 'lucide-react'\nimport { IconButton } from '@/components/ui/IconButton'\nimport { useChat } from '@/contexts/ChatContext'\nimport { handleKeyboardShortcut } from '@/lib/utils'\n\nexport function ChatInput({ onSendMessage, disabled = false }) {\n  const [message, setMessage] = useState('')\n  const [isComposing, setIsComposing] = useState(false)\n  const textareaRef = useRef(null)\n  const { isTyping } = useChat()\n\n  // Auto-resize textarea\n  useEffect(() => {\n    const textarea = textareaRef.current\n    if (textarea) {\n      textarea.style.height = 'auto'\n      textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px'\n    }\n  }, [message])\n\n  // Focus textarea on mount\n  useEffect(() => {\n    if (textareaRef.current) {\n      textareaRef.current.focus()\n    }\n  }, [])\n\n  const handleSubmit = () => {\n    if (!message.trim() || disabled || isTyping) return\n\n    onSendMessage(message.trim())\n    setMessage('')\n\n    // Reset textarea height\n    if (textareaRef.current) {\n      textareaRef.current.style.height = 'auto'\n    }\n  }\n\n  const handleKeyDown = (e) => {\n    // Handle composition events (for IME input)\n    if (isComposing) return\n\n    const shortcuts = [\n      {\n        key: 'enter',\n        ctrl: false,\n        meta: false,\n        alt: false,\n        shift: false,\n        action: handleSubmit\n      }\n    ]\n\n    // Don't submit on Shift+Enter (allow new line)\n    if (e.key === 'Enter' && !e.shiftKey) {\n      handleKeyboardShortcut(e, shortcuts)\n    }\n  }\n\n  const handleCompositionStart = () => {\n    setIsComposing(true)\n  }\n\n  const handleCompositionEnd = () => {\n    setIsComposing(false)\n  }\n\n  const canSend = message.trim() && !disabled && !isTyping\n\n  return (\n    <div className=\"border-t border-border bg-background p-3 sm:p-4\">\n      <div className=\"max-w-4xl mx-auto\">\n        <div className=\"relative flex items-end gap-2 sm:gap-3 bg-input-bg border border-input-border rounded-lg p-2 sm:p-3\">\n          <textarea\n            ref={textareaRef}\n            value={message}\n            onChange={(e) => setMessage(e.target.value)}\n            onKeyDown={handleKeyDown}\n            onCompositionStart={handleCompositionStart}\n            onCompositionEnd={handleCompositionEnd}\n            placeholder={isTyping ? \"ChatGPT is typing...\" : \"Message ChatGPT...\"}\n            disabled={disabled || isTyping}\n            className=\"flex-1 resize-none bg-transparent border-none outline-none placeholder:text-text-muted text-foreground min-h-[24px] max-h-[200px] leading-6\"\n            rows={1}\n            aria-label=\"Message input\"\n            aria-describedby=\"input-help\"\n          />\n\n          <IconButton\n            onClick={handleSubmit}\n            disabled={!canSend}\n            size=\"sm\"\n            variant={canSend ? 'solid' : 'ghost'}\n            className={`\n              flex-shrink-0 transition-all duration-200\n              ${canSend\n                ? 'bg-primary text-white hover:bg-primary/90'\n                : 'text-text-muted cursor-not-allowed'\n              }\n            `}\n            title={canSend ? \"Send message\" : \"Enter a message to send\"}\n          >\n            {isTyping ? <Square size={16} /> : <Send size={16} />}\n          </IconButton>\n        </div>\n\n        <div id=\"input-help\" className=\"text-xs text-text-muted text-center mt-2\">\n          Press Enter to send, Shift+Enter for new line\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAQO,SAAS,UAAU,KAAmC;QAAnC,EAAE,aAAa,EAAE,WAAW,KAAK,EAAE,GAAnC;;IACxB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,cAAc,CAAA,GAAA,4RAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAE3B,uBAAuB;IACvB,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM,WAAW,YAAY,OAAO;YACpC,IAAI,UAAU;gBACZ,SAAS,KAAK,CAAC,MAAM,GAAG;gBACxB,SAAS,KAAK,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,SAAS,YAAY,EAAE,OAAO;YACjE;QACF;8BAAG;QAAC;KAAQ;IAEZ,0BAA0B;IAC1B,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,YAAY,OAAO,EAAE;gBACvB,YAAY,OAAO,CAAC,KAAK;YAC3B;QACF;8BAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI,CAAC,QAAQ,IAAI,MAAM,YAAY,UAAU;QAE7C,cAAc,QAAQ,IAAI;QAC1B,WAAW;QAEX,wBAAwB;QACxB,IAAI,YAAY,OAAO,EAAE;YACvB,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG;QACrC;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,4CAA4C;QAC5C,IAAI,aAAa;QAEjB,MAAM,YAAY;YAChB;gBACE,KAAK;gBACL,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,OAAO;gBACP,QAAQ;YACV;SACD;QAED,+CAA+C;QAC/C,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD,EAAE,GAAG;QAC5B;IACF;IAEA,MAAM,yBAAyB;QAC7B,eAAe;IACjB;IAEA,MAAM,uBAAuB;QAC3B,eAAe;IACjB;IAEA,MAAM,UAAU,QAAQ,IAAI,MAAM,CAAC,YAAY,CAAC;IAEhD,qBACE,4TAAC;QAAI,WAAU;kBACb,cAAA,4TAAC;YAAI,WAAU;;8BACb,4TAAC;oBAAI,WAAU;;sCACb,4TAAC;4BACC,KAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4BAC1C,WAAW;4BACX,oBAAoB;4BACpB,kBAAkB;4BAClB,aAAa,WAAW,yBAAyB;4BACjD,UAAU,YAAY;4BACtB,WAAU;4BACV,MAAM;4BACN,cAAW;4BACX,oBAAiB;;;;;;sCAGnB,4TAAC,wIAAA,CAAA,aAAU;4BACT,SAAS;4BACT,UAAU,CAAC;4BACX,MAAK;4BACL,SAAS,UAAU,UAAU;4BAC7B,WAAW,AAAC,4EAKT,OAHC,UACE,8CACA,sCACH;4BAEH,OAAO,UAAU,iBAAiB;sCAEjC,yBAAW,4TAAC,6RAAA,CAAA,SAAM;gCAAC,MAAM;;;;;qDAAS,4TAAC,yRAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAInD,4TAAC;oBAAI,IAAG;oBAAa,WAAU;8BAA2C;;;;;;;;;;;;;;;;;AAMlF;GA5GgB;;QAIO,iIAAA,CAAA,UAAO;;;KAJd", "debugId": null}}, {"offset": {"line": 1871, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/gpt-interface-next/client/src/components/chat/ChatInterface.js"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './ChatHeader'\nimport { ChatMessages } from './ChatMessages'\nimport { ChatInput } from './ChatInput'\nimport { useChat } from '@/contexts/ChatContext'\nimport { typewriterEffect, sleep } from '@/lib/utils'\n\n// Mock responses for demonstration\nconst mockResponses = [\n  \"Hello! I'm Chat<PERSON><PERSON>, an AI assistant created by OpenAI. I'm here to help you with a wide variety of tasks including:\\n\\n• **Answering questions** on diverse topics\\n• **Writing assistance** for essays, emails, and creative content\\n• **Code help** in multiple programming languages\\n• **Analysis and research** support\\n• **Math and problem-solving**\\n• **Creative projects** like stories and brainstorming\\n\\nWhat would you like to explore today?\",\n\n  \"That's a fascinating question! Let me break this down for you:\\n\\n## Key Points to Consider\\n\\n1. **Primary Factor**: This is the most important element to understand\\n2. **Secondary Considerations**: These build upon the foundation\\n3. **Practical Implications**: How this applies in real-world scenarios\\n\\n### Additional Context\\n\\nIt's worth noting that this topic has evolved significantly over time. The current understanding incorporates both traditional approaches and modern innovations.\\n\\nWould you like me to dive deeper into any specific aspect?\",\n\n  \"I'd be happy to help you with that! Here's a comprehensive example:\\n\\n```javascript\\n// Modern JavaScript example\\nconst fetchUserData = async (userId) => {\\n  try {\\n    const response = await fetch(`/api/users/${userId}`);\\n    \\n    if (!response.ok) {\\n      throw new Error(`HTTP error! status: ${response.status}`);\\n    }\\n    \\n    const userData = await response.json();\\n    return userData;\\n  } catch (error) {\\n    console.error('Error fetching user data:', error);\\n    throw error;\\n  }\\n};\\n\\n// Usage\\nfetchUserData(123)\\n  .then(user => console.log('User:', user))\\n  .catch(err => console.error('Failed to load user:', err));\\n```\\n\\nThis example demonstrates:\\n- **Async/await** for cleaner asynchronous code\\n- **Error handling** with try/catch\\n- **HTTP status checking**\\n- **Proper error propagation**\\n\\nWould you like me to explain any part in more detail?\",\n\n  \"Excellent question! Here's my perspective:\\n\\n> *\\\"The future belongs to those who believe in the beauty of their dreams.\\\"* - Eleanor Roosevelt\\n\\nThis quote captures something profound about human potential and vision. When we combine belief with action, remarkable things become possible.\\n\\n### Why This Matters\\n\\n- **Vision drives innovation**: Great achievements start with someone imagining what could be\\n- **Belief fuels persistence**: When challenges arise, conviction helps us push through\\n- **Dreams inspire others**: Shared visions can mobilize communities and create movements\\n\\n### Practical Application\\n\\nConsider how this applies to your own goals. What dreams are you nurturing? How might you take the next step toward making them reality?\\n\\nI'd love to hear your thoughts on this!\",\n\n  \"I'm excited to help you understand this topic! Let me provide a comprehensive overview:\\n\\n## Core Concepts\\n\\n### Foundation Level\\n- **Concept Alpha**: The fundamental building block that everything else relies on\\n- **Concept Beta**: Extends the foundation with practical applications\\n- **Concept Gamma**: Advanced integration that ties everything together\\n\\n### Real-World Applications\\n\\n1. **Personal Development**\\n   - Daily habits and routines\\n   - Goal setting and achievement\\n   - Continuous learning strategies\\n\\n2. **Professional Context**\\n   - Team collaboration\\n   - Project management\\n   - Leadership development\\n\\n3. **Academic Pursuits**\\n   - Research methodologies\\n   - Critical thinking skills\\n   - Knowledge synthesis\\n\\n### Next Steps\\n\\nTo deepen your understanding, I recommend:\\n- Starting with the foundational concepts\\n- Practicing with real examples\\n- Connecting theory to your specific interests\\n\\nWhat area would you like to explore first?\",\n\n  \"That's a wonderful topic to explore! Let me share some insights:\\n\\n## Understanding the Landscape\\n\\nThis field has seen remarkable evolution in recent years. What once seemed impossible is now becoming routine, thanks to advances in technology and methodology.\\n\\n### Current Trends\\n\\n- **Innovation Acceleration**: New developments are happening faster than ever\\n- **Democratization**: Tools and knowledge are becoming more accessible\\n- **Integration**: Different fields are combining in unexpected ways\\n\\n### Challenges and Opportunities\\n\\n**Challenges:**\\n- Keeping up with rapid change\\n- Ensuring ethical considerations\\n- Managing complexity\\n\\n**Opportunities:**\\n- Unprecedented problem-solving capabilities\\n- Global collaboration potential\\n- Creative expression possibilities\\n\\n### Looking Forward\\n\\nThe most exciting aspect is how this opens doors for creative problem-solving. Whether you're interested in the technical details, practical applications, or broader implications, there's something here for everyone.\\n\\nWhat specific angle interests you most?\"\n]\n\nexport function ChatInterface() {\n  const {\n    currentConversation,\n    addMessage,\n    updateMessage,\n    setIsTyping,\n    createNewConversation\n  } = useChat()\n\n  const handleSendMessage = async (content) => {\n    let conversationId = currentConversation?.id\n\n    // Create new conversation if none exists\n    if (!conversationId) {\n      conversationId = createNewConversation()\n    }\n\n    // Add user message\n    const userMessageId = addMessage(conversationId, {\n      role: 'user',\n      content\n    })\n\n    // Simulate AI response\n    await simulateAIResponse(conversationId)\n  }\n\n  const simulateAIResponse = async (conversationId) => {\n    // Set typing indicator\n    setIsTyping(true)\n\n    // Simulate thinking time\n    await sleep(1000 + Math.random() * 2000)\n\n    // Get random response\n    const response = mockResponses[Math.floor(Math.random() * mockResponses.length)]\n\n    // Add assistant message\n    const assistantMessageId = addMessage(conversationId, {\n      role: 'assistant',\n      content: ''\n    })\n\n    // Clear typing indicator\n    setIsTyping(false)\n\n    // Simulate typewriter effect\n    await typewriterEffect(\n      response,\n      (partialContent) => {\n        updateMessage(conversationId, assistantMessageId, {\n          content: partialContent\n        })\n      },\n      20 // typing speed\n    )\n  }\n\n  return (\n    <div className=\"flex flex-col h-full bg-background\">\n      <ChatHeader />\n      <ChatMessages />\n      <ChatInput onSendMessage={handleSendMessage} />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASA,mCAAmC;AACnC,MAAM,gBAAgB;IACpB;IAEA;IAEA;IAEA;IAEA;IAEA;CACD;AAEM,SAAS;;IACd,MAAM,EACJ,mBAAmB,EACnB,UAAU,EACV,aAAa,EACb,WAAW,EACX,qBAAqB,EACtB,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEV,MAAM,oBAAoB,OAAO;QAC/B,IAAI,iBAAiB,gCAAA,0CAAA,oBAAqB,EAAE;QAE5C,yCAAyC;QACzC,IAAI,CAAC,gBAAgB;YACnB,iBAAiB;QACnB;QAEA,mBAAmB;QACnB,MAAM,gBAAgB,WAAW,gBAAgB;YAC/C,MAAM;YACN;QACF;QAEA,uBAAuB;QACvB,MAAM,mBAAmB;IAC3B;IAEA,MAAM,qBAAqB,OAAO;QAChC,uBAAuB;QACvB,YAAY;QAEZ,yBAAyB;QACzB,MAAM,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE,OAAO,KAAK,MAAM,KAAK;QAEnC,sBAAsB;QACtB,MAAM,WAAW,aAAa,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,cAAc,MAAM,EAAE;QAEhF,wBAAwB;QACxB,MAAM,qBAAqB,WAAW,gBAAgB;YACpD,MAAM;YACN,SAAS;QACX;QAEA,yBAAyB;QACzB,YAAY;QAEZ,6BAA6B;QAC7B,MAAM,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EACnB,UACA,CAAC;YACC,cAAc,gBAAgB,oBAAoB;gBAChD,SAAS;YACX;QACF,GACA,GAAG,eAAe;;IAEtB;IAEA,qBACE,4TAAC;QAAI,WAAU;;0BACb,4TAAC,0IAAA,CAAA,aAAU;;;;;0BACX,4TAAC,4IAAA,CAAA,eAAY;;;;;0BACb,4TAAC,yIAAA,CAAA,YAAS;gBAAC,eAAe;;;;;;;;;;;;AAGhC;GAjEgB;;QAOV,iIAAA,CAAA,UAAO;;;KAPG", "debugId": null}}, {"offset": {"line": 1982, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/gpt-interface-next/client/src/components/ui/KeyboardShortcuts.js"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Keyboard, X } from 'lucide-react'\nimport { IconButton } from './IconButton'\nimport { Button } from './Button'\n\nexport function KeyboardShortcuts() {\n  const [isOpen, setIsOpen] = useState(false)\n\n  const shortcuts = [\n    { key: 'Ctrl + B', description: 'Toggle sidebar' },\n    { key: 'Ctrl + N', description: 'New conversation' },\n    { key: 'Ctrl + D', description: 'Toggle dark mode' },\n    { key: 'Enter', description: 'Send message' },\n    { key: 'Shift + Enter', description: 'New line in message' },\n    { key: 'Escape', description: 'Close dialogs' },\n    { key: 'Tab', description: 'Navigate between elements' },\n    { key: 'Space/Enter', description: 'Activate focused element' },\n    { key: 'Delete/Backspace', description: 'Delete focused conversation' }\n  ]\n\n  useEffect(() => {\n    const handleKeyDown = (e) => {\n      if (e.key === 'Escape') {\n        setIsOpen(false)\n      }\n      if (e.key === '?' && (e.ctrlKey || e.metaKey)) {\n        e.preventDefault()\n        setIsOpen(true)\n      }\n    }\n\n    document.addEventListener('keydown', handleKeyDown)\n    return () => document.removeEventListener('keydown', handleKeyDown)\n  }, [])\n\n  if (!isOpen) {\n    return (\n      <IconButton\n        onClick={() => setIsOpen(true)}\n        variant=\"ghost\"\n        size=\"sm\"\n        title=\"Keyboard shortcuts (Ctrl + ?)\"\n        className=\"fixed bottom-4 right-4 z-50 bg-background border border-border shadow-lg\"\n      >\n        <Keyboard size={16} />\n      </IconButton>\n    )\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4\">\n      <div className=\"bg-background border border-border rounded-lg shadow-xl max-w-md w-full max-h-[80vh] overflow-y-auto\">\n        <div className=\"flex items-center justify-between p-4 border-b border-border\">\n          <h2 className=\"text-lg font-semibold text-foreground\">Keyboard Shortcuts</h2>\n          <IconButton\n            onClick={() => setIsOpen(false)}\n            variant=\"ghost\"\n            size=\"sm\"\n          >\n            <X size={16} />\n          </IconButton>\n        </div>\n        \n        <div className=\"p-4 space-y-3\">\n          {shortcuts.map((shortcut, index) => (\n            <div key={index} className=\"flex items-center justify-between\">\n              <span className=\"text-text-secondary\">{shortcut.description}</span>\n              <kbd className=\"px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded text-xs font-mono border border-border\">\n                {shortcut.key}\n              </kbd>\n            </div>\n          ))}\n        </div>\n        \n        <div className=\"p-4 border-t border-border\">\n          <Button\n            onClick={() => setIsOpen(false)}\n            className=\"w-full\"\n            variant=\"outline\"\n          >\n            Close\n          </Button>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;;;AALA;;;;;AAOO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,YAAY;QAChB;YAAE,KAAK;YAAY,aAAa;QAAiB;QACjD;YAAE,KAAK;YAAY,aAAa;QAAmB;QACnD;YAAE,KAAK;YAAY,aAAa;QAAmB;QACnD;YAAE,KAAK;YAAS,aAAa;QAAe;QAC5C;YAAE,KAAK;YAAiB,aAAa;QAAsB;QAC3D;YAAE,KAAK;YAAU,aAAa;QAAgB;QAC9C;YAAE,KAAK;YAAO,aAAa;QAA4B;QACvD;YAAE,KAAK;YAAe,aAAa;QAA2B;QAC9D;YAAE,KAAK;YAAoB,aAAa;QAA8B;KACvE;IAED,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;6DAAgB,CAAC;oBACrB,IAAI,EAAE,GAAG,KAAK,UAAU;wBACtB,UAAU;oBACZ;oBACA,IAAI,EAAE,GAAG,KAAK,OAAO,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,GAAG;wBAC7C,EAAE,cAAc;wBAChB,UAAU;oBACZ;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;+CAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;;QACvD;sCAAG,EAAE;IAEL,IAAI,CAAC,QAAQ;QACX,qBACE,4TAAC,wIAAA,CAAA,aAAU;YACT,SAAS,IAAM,UAAU;YACzB,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAU;sBAEV,cAAA,4TAAC,iSAAA,CAAA,WAAQ;gBAAC,MAAM;;;;;;;;;;;IAGtB;IAEA,qBACE,4TAAC;QAAI,WAAU;kBACb,cAAA,4TAAC;YAAI,WAAU;;8BACb,4TAAC;oBAAI,WAAU;;sCACb,4TAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,4TAAC,wIAAA,CAAA,aAAU;4BACT,SAAS,IAAM,UAAU;4BACzB,SAAQ;4BACR,MAAK;sCAEL,cAAA,4TAAC,mRAAA,CAAA,IAAC;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAIb,4TAAC;oBAAI,WAAU;8BACZ,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,4TAAC;4BAAgB,WAAU;;8CACzB,4TAAC;oCAAK,WAAU;8CAAuB,SAAS,WAAW;;;;;;8CAC3D,4TAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG;;;;;;;2BAHP;;;;;;;;;;8BASd,4TAAC;oBAAI,WAAU;8BACb,cAAA,4TAAC,oIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,UAAU;wBACzB,WAAU;wBACV,SAAQ;kCACT;;;;;;;;;;;;;;;;;;;;;;AAOX;GAjFgB;KAAA", "debugId": null}}, {"offset": {"line": 2190, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/gpt-interface-next/client/src/components/layout/MainLayout.js"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { Sidebar } from '@/components/sidebar/Sidebar'\nimport { ChatInterface } from '@/components/chat/ChatInterface'\nimport { KeyboardShortcuts } from '@/components/ui/KeyboardShortcuts'\nimport { useChat } from '@/contexts/ChatContext'\nimport { useTheme } from '@/contexts/ThemeContext'\nimport { handleKeyboardShortcut } from '@/lib/utils'\n\nexport function MainLayout() {\n  const { toggleSidebar, createNewConversation } = useChat()\n  const { toggleTheme } = useTheme()\n\n  // Global keyboard shortcuts\n  useEffect(() => {\n    const handleKeyDown = (e) => {\n      const shortcuts = [\n        {\n          key: 'b',\n          ctrl: true,\n          meta: false,\n          alt: false,\n          shift: false,\n          action: toggleSidebar\n        },\n        {\n          key: 'n',\n          ctrl: true,\n          meta: false,\n          alt: false,\n          shift: false,\n          action: createNewConversation\n        },\n        {\n          key: 'd',\n          ctrl: true,\n          meta: false,\n          alt: false,\n          shift: false,\n          action: toggleTheme\n        }\n      ]\n\n      handleKeyboardShortcut(e, shortcuts)\n    }\n\n    document.addEventListener('keydown', handleKeyDown)\n    return () => document.removeEventListener('keydown', handleKeyDown)\n  }, [toggleSidebar, createNewConversation, toggleTheme])\n\n  return (\n    <div className=\"flex h-screen bg-background text-foreground\">\n      <Sidebar />\n      <div className=\"flex-1 flex flex-col min-w-0\">\n        <ChatInterface />\n      </div>\n      <KeyboardShortcuts />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUO,SAAS;;IACd,MAAM,EAAE,aAAa,EAAE,qBAAqB,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IACvD,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD;IAE/B,4BAA4B;IAC5B,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;sDAAgB,CAAC;oBACrB,MAAM,YAAY;wBAChB;4BACE,KAAK;4BACL,MAAM;4BACN,MAAM;4BACN,KAAK;4BACL,OAAO;4BACP,QAAQ;wBACV;wBACA;4BACE,KAAK;4BACL,MAAM;4BACN,MAAM;4BACN,KAAK;4BACL,OAAO;4BACP,QAAQ;wBACV;wBACA;4BACE,KAAK;4BACL,MAAM;4BACN,MAAM;4BACN,KAAK;4BACL,OAAO;4BACP,QAAQ;wBACV;qBACD;oBAED,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD,EAAE,GAAG;gBAC5B;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;wCAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;;QACvD;+BAAG;QAAC;QAAe;QAAuB;KAAY;IAEtD,qBACE,4TAAC;QAAI,WAAU;;0BACb,4TAAC,0IAAA,CAAA,UAAO;;;;;0BACR,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC,6IAAA,CAAA,gBAAa;;;;;;;;;;0BAEhB,4TAAC,+IAAA,CAAA,oBAAiB;;;;;;;;;;;AAGxB;GAlDgB;;QACmC,iIAAA,CAAA,UAAO;QAChC,kIAAA,CAAA,WAAQ;;;KAFlB", "debugId": null}}]}