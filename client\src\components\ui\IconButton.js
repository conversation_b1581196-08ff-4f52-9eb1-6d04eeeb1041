'use client'

import { forwardRef } from 'react'
import { cn } from '@/lib/utils'

const IconButton = forwardRef(({ 
  className, 
  children, 
  size = 'default',
  variant = 'ghost',
  ...props 
}, ref) => {
  const sizeClasses = {
    sm: 'h-8 w-8',
    default: 'h-10 w-10',
    lg: 'h-12 w-12'
  }

  const variantClasses = {
    ghost: 'hover:bg-hover text-text-secondary hover:text-foreground',
    outline: 'border border-border hover:bg-hover',
    solid: 'bg-primary text-white hover:bg-primary/90'
  }

  return (
    <button
      ref={ref}
      className={cn(
        'inline-flex items-center justify-center rounded-md transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
        sizeClasses[size],
        variantClasses[variant],
        className
      )}
      {...props}
    >
      {children}
    </button>
  )
})

IconButton.displayName = 'IconButton'

export { IconButton }
