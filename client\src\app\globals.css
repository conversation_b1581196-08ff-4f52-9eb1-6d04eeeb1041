@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");
@import "tailwindcss";

:root {
  /* Light theme colors */
  --background: #ffffff;
  --foreground: #374151;
  --sidebar-bg: #f7f7f8;
  --border-color: #e5e7eb;
  --user-message-bg: #f7f7f8;
  --assistant-message-bg: #ffffff;
  --primary-green: #10a37f;
  --hover-bg: #f5f5f5;
  --input-bg: #ffffff;
  --input-border: #d1d5db;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;
}

[data-theme="dark"] {
  /* Dark theme colors */
  --background: #202123;
  --foreground: #ececf1;
  --sidebar-bg: #171717;
  --border-color: #4d4d4f;
  --user-message-bg: #2f2f2f;
  --assistant-message-bg: #444654;
  --primary-green: #10a37f;
  --hover-bg: #2a2b32;
  --input-bg: #40414f;
  --input-border: #565869;
  --text-secondary: #c5c5d2;
  --text-muted: #8e8ea0;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-sidebar-bg: var(--sidebar-bg);
  --color-border: var(--border-color);
  --color-user-message: var(--user-message-bg);
  --color-assistant-message: var(--assistant-message-bg);
  --color-primary: var(--primary-green);
  --color-hover: var(--hover-bg);
  --color-input-bg: var(--input-bg);
  --color-input-border: var(--input-border);
  --color-text-secondary: var(--text-secondary);
  --color-text-muted: var(--text-muted);
  --font-sans: "Inter", system-ui, -apple-system, sans-serif;
  --font-mono: var(--font-geist-mono);
}

* {
  box-sizing: border-box;
}

html {
  height: 100%;
}

body {
  height: 100%;
  margin: 0;
  padding: 0;
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  font-size: 14px;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#__next {
  height: 100%;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--color-text-muted);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-secondary);
}

/* Focus styles */
*:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Animation utilities */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

/* Message typing animation */
.typing-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.typing-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--color-text-muted);
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}
