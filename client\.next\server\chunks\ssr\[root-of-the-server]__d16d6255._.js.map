{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/gpt-interface-next/client/src/lib/utils.js"], "sourcesContent": ["import { clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs) {\r\n    return twMerge(clsx(inputs))\r\n}\r\n\r\n// Theme utilities\r\nexport const getSystemTheme = () => {\r\n    if (typeof window !== 'undefined') {\r\n        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'\r\n    }\r\n    return 'light'\r\n}\r\n\r\nexport const setTheme = (theme) => {\r\n    if (typeof window !== 'undefined') {\r\n        const root = document.documentElement\r\n        if (theme === 'dark') {\r\n            root.setAttribute('data-theme', 'dark')\r\n        } else {\r\n            root.removeAttribute('data-theme')\r\n        }\r\n        localStorage.setItem('theme', theme)\r\n    }\r\n}\r\n\r\nexport const getStoredTheme = () => {\r\n    if (typeof window !== 'undefined') {\r\n        return localStorage.getItem('theme') || 'system'\r\n    }\r\n    return 'system'\r\n}\r\n\r\n// Message utilities\r\nexport const generateId = () => {\r\n    return Math.random().toString(36).substring(2, 11)\r\n}\r\n\r\nexport const formatTimestamp = (timestamp) => {\r\n    const date = new Date(timestamp)\r\n    const now = new Date()\r\n    const diffInHours = (now - date) / (1000 * 60 * 60)\r\n\r\n    if (diffInHours < 1) {\r\n        return 'Just now'\r\n    } else if (diffInHours < 24) {\r\n        return `${Math.floor(diffInHours)}h ago`\r\n    } else if (diffInHours < 168) { // 7 days\r\n        return `${Math.floor(diffInHours / 24)}d ago`\r\n    } else {\r\n        return date.toLocaleDateString()\r\n    }\r\n}\r\n\r\nexport const truncateText = (text, maxLength = 50) => {\r\n    if (text.length <= maxLength) return text\r\n    return text.substring(0, maxLength).trim() + '...'\r\n}\r\n\r\n// Conversation utilities\r\nexport const generateConversationTitle = (firstMessage) => {\r\n    if (!firstMessage) return 'New Chat'\r\n\r\n    // Remove markdown and clean up the text\r\n    const cleanText = firstMessage\r\n        .replace(/[#*`_~]/g, '') // Remove markdown characters\r\n        .replace(/\\s+/g, ' ') // Normalize whitespace\r\n        .trim()\r\n\r\n    return truncateText(cleanText, 40)\r\n}\r\n\r\n// Local storage utilities\r\nexport const saveToLocalStorage = (key, data) => {\r\n    if (typeof window !== 'undefined') {\r\n        try {\r\n            localStorage.setItem(key, JSON.stringify(data))\r\n        } catch (error) {\r\n            console.error('Failed to save to localStorage:', error)\r\n        }\r\n    }\r\n}\r\n\r\nexport const loadFromLocalStorage = (key, defaultValue = null) => {\r\n    if (typeof window !== 'undefined') {\r\n        try {\r\n            const item = localStorage.getItem(key)\r\n            return item ? JSON.parse(item) : defaultValue\r\n        } catch (error) {\r\n            console.error('Failed to load from localStorage:', error)\r\n            return defaultValue\r\n        }\r\n    }\r\n    return defaultValue\r\n}\r\n\r\n// Keyboard utilities\r\nexport const isModifierKey = (event) => {\r\n    return event.ctrlKey || event.metaKey || event.altKey\r\n}\r\n\r\nexport const handleKeyboardShortcut = (event, shortcuts) => {\r\n    const key = event.key.toLowerCase()\r\n    const modifiers = {\r\n        ctrl: event.ctrlKey,\r\n        meta: event.metaKey,\r\n        alt: event.altKey,\r\n        shift: event.shiftKey\r\n    }\r\n\r\n    for (const shortcut of shortcuts) {\r\n        if (shortcut.key === key &&\r\n            shortcut.ctrl === modifiers.ctrl &&\r\n            shortcut.meta === modifiers.meta &&\r\n            shortcut.alt === modifiers.alt &&\r\n            shortcut.shift === modifiers.shift) {\r\n            event.preventDefault()\r\n            shortcut.action()\r\n            return true\r\n        }\r\n    }\r\n    return false\r\n}\r\n\r\n// Text processing utilities\r\nexport const copyToClipboard = async (text) => {\r\n    if (typeof window !== 'undefined' && navigator.clipboard) {\r\n        try {\r\n            await navigator.clipboard.writeText(text)\r\n            return true\r\n        } catch (error) {\r\n            console.error('Failed to copy to clipboard:', error)\r\n            return false\r\n        }\r\n    }\r\n    return false\r\n}\r\n\r\n// Animation utilities\r\nexport const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms))\r\n\r\nexport const typewriterEffect = async (text, callback, speed = 30) => {\r\n    for (let i = 0; i <= text.length; i++) {\r\n        callback(text.substring(0, i))\r\n        await sleep(speed)\r\n    }\r\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAM;IACxB,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACxB;AAGO,MAAM,iBAAiB;IAC1B;;IAGA,OAAO;AACX;AAEO,MAAM,WAAW,CAAC;IACrB;;AASJ;AAEO,MAAM,iBAAiB;IAC1B;;IAGA,OAAO;AACX;AAGO,MAAM,aAAa;IACtB,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;AACnD;AAEO,MAAM,kBAAkB,CAAC;IAC5B,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,MAAM,IAAI;IAChB,MAAM,cAAc,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,KAAK,EAAE;IAElD,IAAI,cAAc,GAAG;QACjB,OAAO;IACX,OAAO,IAAI,cAAc,IAAI;QACzB,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,KAAK,CAAC;IAC5C,OAAO,IAAI,cAAc,KAAK;QAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC;IACjD,OAAO;QACH,OAAO,KAAK,kBAAkB;IAClC;AACJ;AAEO,MAAM,eAAe,CAAC,MAAM,YAAY,EAAE;IAC7C,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,WAAW,IAAI,KAAK;AACjD;AAGO,MAAM,4BAA4B,CAAC;IACtC,IAAI,CAAC,cAAc,OAAO;IAE1B,wCAAwC;IACxC,MAAM,YAAY,aACb,OAAO,CAAC,YAAY,IAAI,6BAA6B;KACrD,OAAO,CAAC,QAAQ,KAAK,uBAAuB;KAC5C,IAAI;IAET,OAAO,aAAa,WAAW;AACnC;AAGO,MAAM,qBAAqB,CAAC,KAAK;IACpC;;AAOJ;AAEO,MAAM,uBAAuB,CAAC,KAAK,eAAe,IAAI;IACzD;;IASA,OAAO;AACX;AAGO,MAAM,gBAAgB,CAAC;IAC1B,OAAO,MAAM,OAAO,IAAI,MAAM,OAAO,IAAI,MAAM,MAAM;AACzD;AAEO,MAAM,yBAAyB,CAAC,OAAO;IAC1C,MAAM,MAAM,MAAM,GAAG,CAAC,WAAW;IACjC,MAAM,YAAY;QACd,MAAM,MAAM,OAAO;QACnB,MAAM,MAAM,OAAO;QACnB,KAAK,MAAM,MAAM;QACjB,OAAO,MAAM,QAAQ;IACzB;IAEA,KAAK,MAAM,YAAY,UAAW;QAC9B,IAAI,SAAS,GAAG,KAAK,OACjB,SAAS,IAAI,KAAK,UAAU,IAAI,IAChC,SAAS,IAAI,KAAK,UAAU,IAAI,IAChC,SAAS,GAAG,KAAK,UAAU,GAAG,IAC9B,SAAS,KAAK,KAAK,UAAU,KAAK,EAAE;YACpC,MAAM,cAAc;YACpB,SAAS,MAAM;YACf,OAAO;QACX;IACJ;IACA,OAAO;AACX;AAGO,MAAM,kBAAkB,OAAO;IAClC;;IASA,OAAO;AACX;AAGO,MAAM,QAAQ,CAAC,KAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AAEjE,MAAM,mBAAmB,OAAO,MAAM,UAAU,QAAQ,EAAE;IAC7D,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,MAAM,EAAE,IAAK;QACnC,SAAS,KAAK,SAAS,CAAC,GAAG;QAC3B,MAAM,MAAM;IAChB;AACJ", "debugId": null}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/gpt-interface-next/client/src/contexts/ChatContext.js"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useReducer, useEffect } from 'react'\nimport { generateId, generateConversationTitle, saveToLocalStorage, loadFromLocalStorage } from '@/lib/utils'\n\nconst ChatContext = createContext()\n\n// Action types\nconst ACTIONS = {\n  SET_CONVERSATIONS: 'SET_CONVERSATIONS',\n  ADD_CONVERSATION: 'ADD_CONVERSATION',\n  UPDATE_CONVERSATION: 'UPDATE_CONVERSATION',\n  DELETE_CONVERSATION: 'DELETE_CONVERSATION',\n  SET_CURRENT_CONVERSATION: 'SET_CURRENT_CONVERSATION',\n  ADD_MESSAGE: 'ADD_MESSAGE',\n  UPDATE_MESSAGE: 'UPDATE_MESSAGE',\n  SET_IS_TYPING: 'SET_IS_TYPING',\n  SET_SIDEBAR_OPEN: 'SET_SIDEBAR_OPEN'\n}\n\n// Initial state\nconst initialState = {\n  conversations: [],\n  currentConversationId: null,\n  isTyping: false,\n  sidebarOpen: true\n}\n\n// Reducer function\nfunction chatReducer(state, action) {\n  switch (action.type) {\n    case ACTIONS.SET_CONVERSATIONS:\n      return {\n        ...state,\n        conversations: action.payload\n      }\n\n    case ACTIONS.ADD_CONVERSATION:\n      return {\n        ...state,\n        conversations: [action.payload, ...state.conversations],\n        currentConversationId: action.payload.id\n      }\n\n    case ACTIONS.UPDATE_CONVERSATION:\n      return {\n        ...state,\n        conversations: state.conversations.map(conv =>\n          conv.id === action.payload.id ? { ...conv, ...action.payload.updates } : conv\n        )\n      }\n\n    case ACTIONS.DELETE_CONVERSATION:\n      const filteredConversations = state.conversations.filter(conv => conv.id !== action.payload)\n      const newCurrentId = state.currentConversationId === action.payload\n        ? (filteredConversations.length > 0 ? filteredConversations[0].id : null)\n        : state.currentConversationId\n\n      return {\n        ...state,\n        conversations: filteredConversations,\n        currentConversationId: newCurrentId\n      }\n\n    case ACTIONS.SET_CURRENT_CONVERSATION:\n      return {\n        ...state,\n        currentConversationId: action.payload\n      }\n\n    case ACTIONS.ADD_MESSAGE:\n      return {\n        ...state,\n        conversations: state.conversations.map(conv =>\n          conv.id === action.payload.conversationId\n            ? {\n                ...conv,\n                messages: [...conv.messages, action.payload.message],\n                updatedAt: new Date().toISOString()\n              }\n            : conv\n        )\n      }\n\n    case ACTIONS.UPDATE_MESSAGE:\n      return {\n        ...state,\n        conversations: state.conversations.map(conv =>\n          conv.id === action.payload.conversationId\n            ? {\n                ...conv,\n                messages: conv.messages.map(msg =>\n                  msg.id === action.payload.messageId\n                    ? { ...msg, ...action.payload.updates }\n                    : msg\n                )\n              }\n            : conv\n        )\n      }\n\n    case ACTIONS.SET_IS_TYPING:\n      return {\n        ...state,\n        isTyping: action.payload\n      }\n\n    case ACTIONS.SET_SIDEBAR_OPEN:\n      return {\n        ...state,\n        sidebarOpen: action.payload\n      }\n\n    default:\n      return state\n  }\n}\n\n// Provider component\nexport function ChatProvider({ children }) {\n  const [state, dispatch] = useReducer(chatReducer, initialState)\n\n  // Load conversations from localStorage on mount\n  useEffect(() => {\n    const savedConversations = loadFromLocalStorage('chatgpt-conversations', [])\n    const savedCurrentId = loadFromLocalStorage('chatgpt-current-conversation', null)\n    const savedSidebarState = loadFromLocalStorage('chatgpt-sidebar-open', true)\n\n    if (savedConversations.length > 0) {\n      dispatch({ type: ACTIONS.SET_CONVERSATIONS, payload: savedConversations })\n    }\n    \n    if (savedCurrentId) {\n      dispatch({ type: ACTIONS.SET_CURRENT_CONVERSATION, payload: savedCurrentId })\n    }\n\n    dispatch({ type: ACTIONS.SET_SIDEBAR_OPEN, payload: savedSidebarState })\n  }, [])\n\n  // Save to localStorage whenever conversations change\n  useEffect(() => {\n    if (state.conversations.length > 0) {\n      saveToLocalStorage('chatgpt-conversations', state.conversations)\n    }\n  }, [state.conversations])\n\n  // Save current conversation ID\n  useEffect(() => {\n    if (state.currentConversationId) {\n      saveToLocalStorage('chatgpt-current-conversation', state.currentConversationId)\n    }\n  }, [state.currentConversationId])\n\n  // Save sidebar state\n  useEffect(() => {\n    saveToLocalStorage('chatgpt-sidebar-open', state.sidebarOpen)\n  }, [state.sidebarOpen])\n\n  // Action creators\n  const actions = {\n    createNewConversation: () => {\n      const newConversation = {\n        id: generateId(),\n        title: 'New Chat',\n        messages: [],\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      }\n      dispatch({ type: ACTIONS.ADD_CONVERSATION, payload: newConversation })\n      return newConversation.id\n    },\n\n    deleteConversation: (conversationId) => {\n      dispatch({ type: ACTIONS.DELETE_CONVERSATION, payload: conversationId })\n    },\n\n    setCurrentConversation: (conversationId) => {\n      dispatch({ type: ACTIONS.SET_CURRENT_CONVERSATION, payload: conversationId })\n    },\n\n    addMessage: (conversationId, message) => {\n      const messageWithId = {\n        ...message,\n        id: generateId(),\n        timestamp: new Date().toISOString()\n      }\n      \n      dispatch({\n        type: ACTIONS.ADD_MESSAGE,\n        payload: { conversationId, message: messageWithId }\n      })\n\n      // Update conversation title if this is the first user message\n      const conversation = state.conversations.find(c => c.id === conversationId)\n      if (conversation && conversation.messages.length === 0 && message.role === 'user') {\n        const title = generateConversationTitle(message.content)\n        dispatch({\n          type: ACTIONS.UPDATE_CONVERSATION,\n          payload: { id: conversationId, updates: { title } }\n        })\n      }\n\n      return messageWithId.id\n    },\n\n    updateMessage: (conversationId, messageId, updates) => {\n      dispatch({\n        type: ACTIONS.UPDATE_MESSAGE,\n        payload: { conversationId, messageId, updates }\n      })\n    },\n\n    setIsTyping: (isTyping) => {\n      dispatch({ type: ACTIONS.SET_IS_TYPING, payload: isTyping })\n    },\n\n    toggleSidebar: () => {\n      dispatch({ type: ACTIONS.SET_SIDEBAR_OPEN, payload: !state.sidebarOpen })\n    },\n\n    setSidebarOpen: (open) => {\n      dispatch({ type: ACTIONS.SET_SIDEBAR_OPEN, payload: open })\n    }\n  }\n\n  const value = {\n    ...state,\n    ...actions,\n    currentConversation: state.conversations.find(c => c.id === state.currentConversationId) || null\n  }\n\n  return (\n    <ChatContext.Provider value={value}>\n      {children}\n    </ChatContext.Provider>\n  )\n}\n\n// Hook to use the chat context\nexport function useChat() {\n  const context = useContext(ChatContext)\n  if (!context) {\n    throw new Error('useChat must be used within a ChatProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,gBAAa,AAAD;AAEhC,eAAe;AACf,MAAM,UAAU;IACd,mBAAmB;IACnB,kBAAkB;IAClB,qBAAqB;IACrB,qBAAqB;IACrB,0BAA0B;IAC1B,aAAa;IACb,gBAAgB;IAChB,eAAe;IACf,kBAAkB;AACpB;AAEA,gBAAgB;AAChB,MAAM,eAAe;IACnB,eAAe,EAAE;IACjB,uBAAuB;IACvB,UAAU;IACV,aAAa;AACf;AAEA,mBAAmB;AACnB,SAAS,YAAY,KAAK,EAAE,MAAM;IAChC,OAAQ,OAAO,IAAI;QACjB,KAAK,QAAQ,iBAAiB;YAC5B,OAAO;gBACL,GAAG,KAAK;gBACR,eAAe,OAAO,OAAO;YAC/B;QAEF,KAAK,QAAQ,gBAAgB;YAC3B,OAAO;gBACL,GAAG,KAAK;gBACR,eAAe;oBAAC,OAAO,OAAO;uBAAK,MAAM,aAAa;iBAAC;gBACvD,uBAAuB,OAAO,OAAO,CAAC,EAAE;YAC1C;QAEF,KAAK,QAAQ,mBAAmB;YAC9B,OAAO;gBACL,GAAG,KAAK;gBACR,eAAe,MAAM,aAAa,CAAC,GAAG,CAAC,CAAA,OACrC,KAAK,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE,GAAG;wBAAE,GAAG,IAAI;wBAAE,GAAG,OAAO,OAAO,CAAC,OAAO;oBAAC,IAAI;YAE7E;QAEF,KAAK,QAAQ,mBAAmB;YAC9B,MAAM,wBAAwB,MAAM,aAAa,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,OAAO;YAC3F,MAAM,eAAe,MAAM,qBAAqB,KAAK,OAAO,OAAO,GAC9D,sBAAsB,MAAM,GAAG,IAAI,qBAAqB,CAAC,EAAE,CAAC,EAAE,GAAG,OAClE,MAAM,qBAAqB;YAE/B,OAAO;gBACL,GAAG,KAAK;gBACR,eAAe;gBACf,uBAAuB;YACzB;QAEF,KAAK,QAAQ,wBAAwB;YACnC,OAAO;gBACL,GAAG,KAAK;gBACR,uBAAuB,OAAO,OAAO;YACvC;QAEF,KAAK,QAAQ,WAAW;YACtB,OAAO;gBACL,GAAG,KAAK;gBACR,eAAe,MAAM,aAAa,CAAC,GAAG,CAAC,CAAA,OACrC,KAAK,EAAE,KAAK,OAAO,OAAO,CAAC,cAAc,GACrC;wBACE,GAAG,IAAI;wBACP,UAAU;+BAAI,KAAK,QAAQ;4BAAE,OAAO,OAAO,CAAC,OAAO;yBAAC;wBACpD,WAAW,IAAI,OAAO,WAAW;oBACnC,IACA;YAER;QAEF,KAAK,QAAQ,cAAc;YACzB,OAAO;gBACL,GAAG,KAAK;gBACR,eAAe,MAAM,aAAa,CAAC,GAAG,CAAC,CAAA,OACrC,KAAK,EAAE,KAAK,OAAO,OAAO,CAAC,cAAc,GACrC;wBACE,GAAG,IAAI;wBACP,UAAU,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAA,MAC1B,IAAI,EAAE,KAAK,OAAO,OAAO,CAAC,SAAS,GAC/B;gCAAE,GAAG,GAAG;gCAAE,GAAG,OAAO,OAAO,CAAC,OAAO;4BAAC,IACpC;oBAER,IACA;YAER;QAEF,KAAK,QAAQ,aAAa;YACxB,OAAO;gBACL,GAAG,KAAK;gBACR,UAAU,OAAO,OAAO;YAC1B;QAEF,KAAK,QAAQ,gBAAgB;YAC3B,OAAO;gBACL,GAAG,KAAK;gBACR,aAAa,OAAO,OAAO;YAC7B;QAEF;YACE,OAAO;IACX;AACF;AAGO,SAAS,aAAa,EAAE,QAAQ,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,aAAU,AAAD,EAAE,aAAa;IAElD,gDAAgD;IAChD,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAA,GAAA,mHAAA,CAAA,uBAAoB,AAAD,EAAE,yBAAyB,EAAE;QAC3E,MAAM,iBAAiB,CAAA,GAAA,mHAAA,CAAA,uBAAoB,AAAD,EAAE,gCAAgC;QAC5E,MAAM,oBAAoB,CAAA,GAAA,mHAAA,CAAA,uBAAoB,AAAD,EAAE,wBAAwB;QAEvE,IAAI,mBAAmB,MAAM,GAAG,GAAG;YACjC,SAAS;gBAAE,MAAM,QAAQ,iBAAiB;gBAAE,SAAS;YAAmB;QAC1E;QAEA,IAAI,gBAAgB;YAClB,SAAS;gBAAE,MAAM,QAAQ,wBAAwB;gBAAE,SAAS;YAAe;QAC7E;QAEA,SAAS;YAAE,MAAM,QAAQ,gBAAgB;YAAE,SAAS;QAAkB;IACxE,GAAG,EAAE;IAEL,qDAAqD;IACrD,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM,aAAa,CAAC,MAAM,GAAG,GAAG;YAClC,CAAA,GAAA,mHAAA,CAAA,qBAAkB,AAAD,EAAE,yBAAyB,MAAM,aAAa;QACjE;IACF,GAAG;QAAC,MAAM,aAAa;KAAC;IAExB,+BAA+B;IAC/B,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM,qBAAqB,EAAE;YAC/B,CAAA,GAAA,mHAAA,CAAA,qBAAkB,AAAD,EAAE,gCAAgC,MAAM,qBAAqB;QAChF;IACF,GAAG;QAAC,MAAM,qBAAqB;KAAC;IAEhC,qBAAqB;IACrB,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,CAAA,GAAA,mHAAA,CAAA,qBAAkB,AAAD,EAAE,wBAAwB,MAAM,WAAW;IAC9D,GAAG;QAAC,MAAM,WAAW;KAAC;IAEtB,kBAAkB;IAClB,MAAM,UAAU;QACd,uBAAuB;YACrB,MAAM,kBAAkB;gBACtB,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;gBACb,OAAO;gBACP,UAAU,EAAE;gBACZ,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC;YACA,SAAS;gBAAE,MAAM,QAAQ,gBAAgB;gBAAE,SAAS;YAAgB;YACpE,OAAO,gBAAgB,EAAE;QAC3B;QAEA,oBAAoB,CAAC;YACnB,SAAS;gBAAE,MAAM,QAAQ,mBAAmB;gBAAE,SAAS;YAAe;QACxE;QAEA,wBAAwB,CAAC;YACvB,SAAS;gBAAE,MAAM,QAAQ,wBAAwB;gBAAE,SAAS;YAAe;QAC7E;QAEA,YAAY,CAAC,gBAAgB;YAC3B,MAAM,gBAAgB;gBACpB,GAAG,OAAO;gBACV,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;gBACb,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,SAAS;gBACP,MAAM,QAAQ,WAAW;gBACzB,SAAS;oBAAE;oBAAgB,SAAS;gBAAc;YACpD;YAEA,8DAA8D;YAC9D,MAAM,eAAe,MAAM,aAAa,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC5D,IAAI,gBAAgB,aAAa,QAAQ,CAAC,MAAM,KAAK,KAAK,QAAQ,IAAI,KAAK,QAAQ;gBACjF,MAAM,QAAQ,CAAA,GAAA,mHAAA,CAAA,4BAAyB,AAAD,EAAE,QAAQ,OAAO;gBACvD,SAAS;oBACP,MAAM,QAAQ,mBAAmB;oBACjC,SAAS;wBAAE,IAAI;wBAAgB,SAAS;4BAAE;wBAAM;oBAAE;gBACpD;YACF;YAEA,OAAO,cAAc,EAAE;QACzB;QAEA,eAAe,CAAC,gBAAgB,WAAW;YACzC,SAAS;gBACP,MAAM,QAAQ,cAAc;gBAC5B,SAAS;oBAAE;oBAAgB;oBAAW;gBAAQ;YAChD;QACF;QAEA,aAAa,CAAC;YACZ,SAAS;gBAAE,MAAM,QAAQ,aAAa;gBAAE,SAAS;YAAS;QAC5D;QAEA,eAAe;YACb,SAAS;gBAAE,MAAM,QAAQ,gBAAgB;gBAAE,SAAS,CAAC,MAAM,WAAW;YAAC;QACzE;QAEA,gBAAgB,CAAC;YACf,SAAS;gBAAE,MAAM,QAAQ,gBAAgB;gBAAE,SAAS;YAAK;QAC3D;IACF;IAEA,MAAM,QAAQ;QACZ,GAAG,KAAK;QACR,GAAG,OAAO;QACV,qBAAqB,MAAM,aAAa,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,qBAAqB,KAAK;IAC9F;IAEA,qBACE,6WAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAGO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,oUAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/gpt-interface-next/client/src/contexts/ThemeContext.js"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useState, useEffect } from 'react'\nimport { getSystemTheme, setTheme as setThemeUtil, getStoredTheme } from '@/lib/utils'\n\nconst ThemeContext = createContext()\n\nexport function ThemeProvider({ children }) {\n  const [theme, setThemeState] = useState('system')\n  const [resolvedTheme, setResolvedTheme] = useState('light')\n\n  // Initialize theme on mount\n  useEffect(() => {\n    const storedTheme = getStoredTheme()\n    setThemeState(storedTheme)\n    \n    const resolved = storedTheme === 'system' ? getSystemTheme() : storedTheme\n    setResolvedTheme(resolved)\n    setThemeUtil(resolved)\n  }, [])\n\n  // Listen for system theme changes\n  useEffect(() => {\n    if (theme === 'system') {\n      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')\n      \n      const handleChange = (e) => {\n        const newTheme = e.matches ? 'dark' : 'light'\n        setResolvedTheme(newTheme)\n        setThemeUtil(newTheme)\n      }\n\n      mediaQuery.addEventListener('change', handleChange)\n      return () => mediaQuery.removeEventListener('change', handleChange)\n    }\n  }, [theme])\n\n  const setTheme = (newTheme) => {\n    setThemeState(newTheme)\n    \n    const resolved = newTheme === 'system' ? getSystemTheme() : newTheme\n    setResolvedTheme(resolved)\n    setThemeUtil(resolved)\n  }\n\n  const toggleTheme = () => {\n    const newTheme = resolvedTheme === 'light' ? 'dark' : 'light'\n    setTheme(newTheme)\n  }\n\n  const value = {\n    theme,\n    resolvedTheme,\n    setTheme,\n    toggleTheme,\n    isDark: resolvedTheme === 'dark'\n  }\n\n  return (\n    <ThemeContext.Provider value={value}>\n      {children}\n    </ThemeContext.Provider>\n  )\n}\n\nexport function useTheme() {\n  const context = useContext(ThemeContext)\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,6BAAe,CAAA,GAAA,oUAAA,CAAA,gBAAa,AAAD;AAE1B,SAAS,cAAc,EAAE,QAAQ,EAAE;IACxC,MAAM,CAAC,OAAO,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACxC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,4BAA4B;IAC5B,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD;QACjC,cAAc;QAEd,MAAM,WAAW,gBAAgB,WAAW,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,MAAM;QAC/D,iBAAiB;QACjB,CAAA,GAAA,mHAAA,CAAA,WAAY,AAAD,EAAE;IACf,GAAG,EAAE;IAEL,kCAAkC;IAClC,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,UAAU;YACtB,MAAM,aAAa,OAAO,UAAU,CAAC;YAErC,MAAM,eAAe,CAAC;gBACpB,MAAM,WAAW,EAAE,OAAO,GAAG,SAAS;gBACtC,iBAAiB;gBACjB,CAAA,GAAA,mHAAA,CAAA,WAAY,AAAD,EAAE;YACf;YAEA,WAAW,gBAAgB,CAAC,UAAU;YACtC,OAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;QACxD;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,WAAW,CAAC;QAChB,cAAc;QAEd,MAAM,WAAW,aAAa,WAAW,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,MAAM;QAC5D,iBAAiB;QACjB,CAAA,GAAA,mHAAA,CAAA,WAAY,AAAD,EAAE;IACf;IAEA,MAAM,cAAc;QAClB,MAAM,WAAW,kBAAkB,UAAU,SAAS;QACtD,SAAS;IACX;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA,QAAQ,kBAAkB;IAC5B;IAEA,qBACE,6WAAC,aAAa,QAAQ;QAAC,OAAO;kBAC3B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,oUAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}]}